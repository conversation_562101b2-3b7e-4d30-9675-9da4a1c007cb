/* Alpine.js + HTMX Demo Styles */

/* Alpine.js Transition Classes */
.fade-enter {
  opacity: 0;
}
.fade-enter-active {
  opacity: 0;
  transition: opacity 300ms ease-in;
}
.fade-enter-to {
  opacity: 1;
}
.fade-leave {
  opacity: 1;
}
.fade-leave-active {
  opacity: 1;
  transition: opacity 300ms ease-out;
}
.fade-leave-to {
  opacity: 0;
}

.slide-enter {
  transform: translateX(-100%);
}
.slide-enter-active {
  transform: translateX(-100%);
  transition: transform 300ms ease-in;
}
.slide-enter-to {
  transform: translateX(0);
}
.slide-leave {
  transform: translateX(0);
}
.slide-leave-active {
  transform: translateX(0);
  transition: transform 300ms ease-out;
}
.slide-leave-to {
  transform: translateX(-100%);
}

.scale-enter {
  transform: scale(0.95);
  opacity: 0;
}
.scale-enter-active {
  transform: scale(0.95);
  opacity: 0;
  transition: transform 200ms ease-out, opacity 200ms ease-out;
}
.scale-enter-to {
  transform: scale(1);
  opacity: 1;
}
.scale-leave {
  transform: scale(1);
  opacity: 1;
}
.scale-leave-active {
  transform: scale(1);
  opacity: 1;
  transition: transform 200ms ease-in, opacity 200ms ease-in;
}
.scale-leave-to {
  transform: scale(0.95);
  opacity: 0;
}

/* Table row transitions */
.table-row-enter {
  opacity: 0;
  transform: translateY(-10px);
}
.table-row-enter-active {
  opacity: 0;
  transform: translateY(-10px);
  transition: opacity 300ms ease-out, transform 300ms ease-out;
}
.table-row-enter-to {
  opacity: 1;
  transform: translateY(0);
}
.table-row-leave {
  opacity: 1;
  transform: translateY(0);
}
.table-row-leave-active {
  opacity: 1;
  transform: translateY(0);
  transition: opacity 300ms ease-in, transform 300ms ease-in;
}
.table-row-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

/* HTMX Loading States */
.htmx-request {
  opacity: 0.6;
  transition: opacity 200ms ease;
}

.htmx-request .btn {
  pointer-events: none;
}

/* Additional utility animations */
@keyframes shake {
  0%, 100% { transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
  20%, 40%, 60%, 80% { transform: translateX(5px); }
}

@keyframes flash {
  0%, 100% { background-color: transparent; }
  50% { background-color: #28a745; }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.animate-shake {
  animation: shake 0.5s ease-in-out;
}

.animate-flash {
  animation: flash 1s ease-in-out;
}

.animate-pulse {
  animation: pulse 2s infinite;
}

/* Loading states */
.htmx-loading {
  position: relative;
  pointer-events: none;
}

.htmx-loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Alpine.js cloak */
[x-cloak] {
  display: none !important;
}

/* Smooth transitions for all interactive elements */
.btn, .form-control {
  transition: all 0.2s ease;
}

.btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.form-control:focus {
  transform: scale(1.02);
  box-shadow: 0 0 0 3px rgba(0,123,255,0.25);
}

/* Custom transition utilities */
.transition-all {
  transition: all 0.2s ease;
}

.duration-200 {
  transition-duration: 200ms;
}

.hover\:scale-105:hover {
  transform: scale(1.05);
}

/* Demo specific styles */
.demo-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.demo-card {
  margin-bottom: 2rem;
  border: 1px solid #dee2e6;
  border-radius: 0.375rem;
  overflow: hidden;
}

.demo-card-header {
  background-color: #f8f9fa;
  padding: 1rem;
  border-bottom: 1px solid #dee2e6;
}

.demo-card-body {
  padding: 1rem;
}

/* Loading spinner */
.spinner-border {
  display: inline-block;
  width: 2rem;
  height: 2rem;
  vertical-align: text-bottom;
  border: 0.25em solid currentColor;
  border-right-color: transparent;
  border-radius: 50%;
  animation: spinner-border 0.75s linear infinite;
}

@keyframes spinner-border {
  to {
    transform: rotate(360deg);
  }
}

.text-primary {
  color: #0d6efd !important;
}

.visually-hidden {
  position: absolute !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  margin: -1px !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border: 0 !important;
}
