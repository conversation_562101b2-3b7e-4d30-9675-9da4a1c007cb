<!DOCTYPE html>
<html>
<head>
    <title>Alpine.js + HTMX Transitions Test</title>
    <script src="https://unpkg.com/htmx.org@2.0.2"></script>
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        /* Simple transition styles */
        [x-cloak] { display: none !important; }
        
        .fade-transition {
            transition: opacity 300ms ease;
        }
        
        .htmx-request {
            opacity: 0.6;
            transition: opacity 200ms ease;
        }
        
        .btn {
            transition: all 0.2s ease;
        }
        
        .btn:hover {
            transform: translateY(-1px);
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1>Alpine.js + HTMX Transitions Test</h1>
        
        <!-- Test 1: Basic Alpine.js functionality -->
        <div class="card mb-4">
            <div class="card-header">
                <h3>Test 1: Basic Alpine.js</h3>
            </div>
            <div class="card-body">
                <div x-data="{ count: 0 }">
                    <button @click="count++" class="btn btn-primary">Count: <span x-text="count"></span></button>
                    <p x-show="count > 0" x-transition class="mt-2 alert alert-info">
                        You clicked <span x-text="count"></span> times!
                    </p>
                </div>
            </div>
        </div>

        <!-- Test 2: Basic HTMX functionality -->
        <div class="card mb-4">
            <div class="card-header">
                <h3>Test 2: Basic HTMX</h3>
            </div>
            <div class="card-body">
                <button 
                    hx-get="api_e.php?action=example_action" 
                    hx-target="#htmx-result"
                    hx-vals='{"test": "basic"}'
                    class="btn btn-success"
                >
                    Basic HTMX Request
                </button>
                <div id="htmx-result" class="mt-3 p-3 border rounded">
                    Results will appear here...
                </div>
            </div>
        </div>

        <!-- Test 3: Alpine.js with HTMX loading state -->
        <div class="card mb-4">
            <div class="card-header">
                <h3>Test 3: Alpine.js + HTMX Loading State</h3>
            </div>
            <div class="card-body">
                <div x-data="{ loading: false }">
                    <button 
                        hx-get="api_e.php?action=example_action" 
                        hx-target="#loading-result"
                        hx-vals='{"test": "loading"}'
                        @htmx:before-request="loading = true"
                        @htmx:after-request="loading = false"
                        :disabled="loading"
                        class="btn btn-info"
                    >
                        <span x-show="!loading">Click for Loading Test</span>
                        <span x-show="loading" x-cloak>Loading...</span>
                    </button>
                    <div id="loading-result" class="mt-3 p-3 border rounded">
                        Results will appear here...
                    </div>
                </div>
            </div>
        </div>

        <!-- Test 4: Content with transitions -->
        <div class="card mb-4">
            <div class="card-header">
                <h3>Test 4: Content Transitions</h3>
            </div>
            <div class="card-body">
                <button 
                    hx-get="api_e.php?action=get_content" 
                    hx-target="#transition-content"
                    class="btn btn-warning"
                >
                    Load Content with Transition
                </button>
                <div id="transition-content" class="mt-3 p-3 border rounded fade-transition">
                    <p class="text-muted">Initial content - click button to replace</p>
                </div>
            </div>
        </div>

        <!-- Test 5: Table row with Alpine.js -->
        <div class="card mb-4">
            <div class="card-header">
                <h3>Test 5: Table Row Transitions</h3>
            </div>
            <div class="card-body">
                <button 
                    hx-post="api_e.php?action=add_row" 
                    hx-target="#test-table tbody"
                    hx-swap="beforeend"
                    class="btn btn-success mb-3"
                >
                    Add Row
                </button>
                
                <table id="test-table" class="table table-striped">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Name</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr x-data="{ show: true }" x-show="show" x-transition>
                            <td>1</td>
                            <td>Test Item</td>
                            <td>Active</td>
                            <td>
                                <button @click="show = false" class="btn btn-sm btn-danger">
                                    Remove
                                </button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Test 6: Manual transition test -->
        <div class="card mb-4">
            <div class="card-header">
                <h3>Test 6: Manual Transitions</h3>
            </div>
            <div class="card-body">
                <div x-data="{ visible: true }">
                    <button @click="visible = !visible" class="btn btn-primary">
                        Toggle Content
                    </button>
                    <div x-show="visible" x-transition class="mt-3 alert alert-success">
                        <h5>This content should fade in/out</h5>
                        <p>If you can see smooth transitions here, Alpine.js is working correctly.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Debug logging
        console.log('Test page loaded');
        
        // Check if Alpine.js is available
        document.addEventListener('alpine:init', () => {
            console.log('✅ Alpine.js is working!');
        });
        
        // Check HTMX events
        document.body.addEventListener('htmx:beforeRequest', (e) => {
            console.log('🚀 HTMX request starting:', e.detail);
        });
        
        document.body.addEventListener('htmx:afterRequest', (e) => {
            console.log('✅ HTMX request completed:', e.detail);
        });
        
        document.body.addEventListener('htmx:beforeSwap', (e) => {
            console.log('🔄 HTMX before swap:', e.detail);
            // Add fade effect
            const target = e.detail.target;
            if (target) {
                target.style.transition = 'opacity 200ms ease';
                target.style.opacity = '0.5';
            }
        });
        
        document.body.addEventListener('htmx:afterSwap', (e) => {
            console.log('✨ HTMX after swap:', e.detail);
            // Restore opacity
            const target = e.detail.target;
            if (target) {
                setTimeout(() => {
                    target.style.opacity = '1';
                }, 50);
            }
        });
    </script>
</body>
</html>
