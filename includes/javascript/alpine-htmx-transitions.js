/**
 * Alpine.js and HTMX Integration for Enhanced Transitions
 * This file provides utilities for smooth transitions between HTMX requests
 */

// Debug Alpine.js loading
console.log('Alpine HTMX Transitions script loaded');

// Alpine.js global data and utilities
document.addEventListener('alpine:init', () => {
    console.log('Alpine.js initialized');

    // Global transition utilities
    Alpine.data('transitionUtils', () => ({
        // Fade transition helper
        fadeTransition: {
            'x-transition:enter': 'transition ease-out duration-300',
            'x-transition:enter-start': 'opacity-0',
            'x-transition:enter-end': 'opacity-100',
            'x-transition:leave': 'transition ease-in duration-200',
            'x-transition:leave-start': 'opacity-100',
            'x-transition:leave-end': 'opacity-0'
        },

        // Slide transition helper
        slideTransition: {
            'x-transition:enter': 'transition ease-out duration-300',
            'x-transition:enter-start': 'transform -translate-x-full',
            'x-transition:enter-end': 'transform translate-x-0',
            'x-transition:leave': 'transition ease-in duration-200',
            'x-transition:leave-start': 'transform translate-x-0',
            'x-transition:leave-end': 'transform -translate-x-full'
        },

        // Scale transition helper
        scaleTransition: {
            'x-transition:enter': 'transition ease-out duration-200',
            'x-transition:enter-start': 'transform scale-95 opacity-0',
            'x-transition:enter-end': 'transform scale-100 opacity-100',
            'x-transition:leave': 'transition ease-in duration-150',
            'x-transition:leave-start': 'transform scale-100 opacity-100',
            'x-transition:leave-end': 'transform scale-95 opacity-0'
        }
    }));

    // Loading state management
    Alpine.data('loadingState', () => ({
        loading: false,
        loadingText: 'Loading...',

        startLoading(text = 'Loading...') {
            this.loading = true;
            this.loadingText = text;
        },

        stopLoading() {
            this.loading = false;
        }
    }));

    // Table row management with transitions
    Alpine.data('tableRow', () => ({
        show: true,
        removing: false,

        remove() {
            this.removing = true;
            setTimeout(() => {
                this.show = false;
            }, 150);
        },

        fadeOut() {
            this.show = false;
        }
    }));

    // Form state management
    Alpine.data('formState', () => ({
        submitting: false,
        errors: {},
        success: false,

        submit() {
            this.submitting = true;
            this.errors = {};
            this.success = false;
        },

        handleSuccess() {
            this.submitting = false;
            this.success = true;
            setTimeout(() => {
                this.success = false;
            }, 3000);
        },

        handleError(errors = {}) {
            this.submitting = false;
            this.errors = errors;
        }
    }));
});

// HTMX event listeners for enhanced transitions
document.addEventListener('DOMContentLoaded', function() {
    console.log('HTMX event listeners initialized');

    // Add transition classes to HTMX targets
    document.body.addEventListener('htmx:beforeSwap', function(evt) {
        console.log('HTMX beforeSwap event', evt.detail);
        const target = evt.detail.target;

        // Add fade-out class before content swap
        if (target && !target.classList.contains('no-transition')) {
            target.style.transition = 'opacity 200ms ease-out';
            target.style.opacity = '0.6';
        }
    });

    // Restore opacity after swap
    document.body.addEventListener('htmx:afterSwap', function(evt) {
        const target = evt.detail.target;

        if (target && !target.classList.contains('no-transition')) {
            setTimeout(() => {
                target.style.opacity = '1';
            }, 50);
        }
    });

    // Enhanced loading indicators
    document.body.addEventListener('htmx:beforeRequest', function(evt) {
        const trigger = evt.detail.elt;

        // Add loading class to trigger element
        if (trigger) {
            trigger.classList.add('htmx-loading');
        }

        // Show global loading indicator if it exists
        const indicator = document.getElementById('indicatorLines');
        if (indicator) {
            indicator.style.display = 'block';
            indicator.style.opacity = '1';
        }
    });

    document.body.addEventListener('htmx:afterRequest', function(evt) {
        const trigger = evt.detail.elt;

        // Remove loading class from trigger element
        if (trigger) {
            trigger.classList.remove('htmx-loading');
        }

        // Hide global loading indicator
        const indicator = document.getElementById('indicatorLines');
        if (indicator) {
            setTimeout(() => {
                indicator.style.opacity = '0';
                setTimeout(() => {
                    indicator.style.display = 'none';
                }, 200);
            }, 100);
        }
    });

    // Error handling with transitions
    document.body.addEventListener('htmx:responseError', function(evt) {
        const target = evt.detail.target;

        // Add error shake animation
        if (target) {
            target.classList.add('animate-shake');
            setTimeout(() => {
                target.classList.remove('animate-shake');
            }, 500);
        }
    });

    // Success animations for form submissions
    document.body.addEventListener('htmx:afterRequest', function(evt) {
        if (evt.detail.successful && evt.detail.xhr.status === 200) {
            const target = evt.detail.target;

            // Add success flash animation
            if (target && target.tagName === 'TR') {
                target.classList.add('animate-flash');
                setTimeout(() => {
                    target.classList.remove('animate-flash');
                }, 1000);
            }
        }
    });
});

// Utility functions for manual transition control
window.AlpineHTMXUtils = {
    // Fade in an element
    fadeIn: function(element, duration = 300) {
        element.style.opacity = '0';
        element.style.transition = `opacity ${duration}ms ease-in`;
        element.style.display = 'block';

        setTimeout(() => {
            element.style.opacity = '1';
        }, 10);
    },

    // Fade out an element
    fadeOut: function(element, duration = 300) {
        element.style.transition = `opacity ${duration}ms ease-out`;
        element.style.opacity = '0';

        setTimeout(() => {
            element.style.display = 'none';
        }, duration);
    },

    // Slide in an element
    slideIn: function(element, direction = 'left', duration = 300) {
        const transform = direction === 'left' ? 'translateX(-100%)' : 'translateX(100%)';
        element.style.transform = transform;
        element.style.transition = `transform ${duration}ms ease-out`;
        element.style.display = 'block';

        setTimeout(() => {
            element.style.transform = 'translateX(0)';
        }, 10);
    },

    // Scale in an element
    scaleIn: function(element, duration = 200) {
        element.style.transform = 'scale(0.95)';
        element.style.opacity = '0';
        element.style.transition = `transform ${duration}ms ease-out, opacity ${duration}ms ease-out`;
        element.style.display = 'block';

        setTimeout(() => {
            element.style.transform = 'scale(1)';
            element.style.opacity = '1';
        }, 10);
    }
};
