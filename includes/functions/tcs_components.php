<?php


/**
 * Generates a bootstrap button with htmx attributes
 *
 * @param string $method The method to use for the request. Defaults to 'post'
 * @param string $endpoint The endpoint to make the request to
 * @param string $target The target element to update with the response
 * @param string $swap The swap mode to use. Defaults to 'outerHTML'
 * @param string $text The text to display on the button
 * @param array $vals An array of values to include in the request
 * @param array $extra_params An array of extra parameters to include in the button element
 *
 * @return string The HTML for the button
 */
function tcs_draw_button_html(string $method = null, string $endpoint = null, string $target = null, string $swap = null, string $text = null, array|null $vals = null, array|null $extra_params = [], string $class ='', bool $with_transitions = true)
{
    $vals_attribute = $vals ? " hx-vals='" . json_encode($vals) . "'" : '';
    $method = ($method != null) ?  $method = 'post' : '';
    $method = ($endpoint != null) ?  ' hx-' . $method . '="' . $endpoint . '"' : '';
    $endpoint = ($endpoint != null) ?  ' hx-endpoint="' . $endpoint . '"' : '';
    $target = ($target != null) ?  ' hx-target="' . $target . '"' : '';
    $swap = ($swap != null) ?  ' hx-swap="' . $swap . '"' : '';
    $vals = ($vals != null) ?  ' hx-vals="' . json_encode($vals) . '"' : '';

    // Add Alpine.js transitions if enabled
    $alpine_attrs = '';
    if ($with_transitions) {
        $alpine_attrs = ' x-data="{ loading: false }" x-on:htmx:before-request="loading = true" x-on:htmx:after-request="loading = false" x-bind:disabled="loading"';
        $class .= ' transition-all duration-200 hover:scale-105';
    }

    foreach ($extra_params as $key => $value) {
        $extra_params .= ' ' . $key . '="' . $value . '"';
    }

    $loading_text = $with_transitions ? '<span x-show="!loading">' . $text . '</span><span x-show="loading" x-cloak>Loading...</span>' : $text;

    return '<button ' . $method . ' hx-indicator="#indicatorLines"' . $target . $swap . $vals_attribute . ' class="btn btn-primary btn-sm btn_send ' . $class . '"' . $extra_params . $alpine_attrs . '>' . $loading_text . '</button>&nbsp;';
}

/**
 * Generates a bootstrap card html element with a title, text and optional class and name.
 * @param string $id The id of the card element.
 * @param string $title The title of the card.
 * @param string $text The text content of the card.
 * @param string $class The class of the card element.
 * @param string $name The name of the card element.
 * @return string The generated html.
 */
function tcs_draw_card_html(string $id, string $title, string $text, string $class = '', string $name = '')
{
    return '
        <div class="card" id="' . $id . '">
          <div class="card-header">
            <h3 class="card-title">' . $title . '</h3>
          </div>
          <div class="card-body">
            <p>' . $text . '</p>
          </div>
          <!-- /.card-body -->
        </div>
        <!-- /.card -->';
}

function tcs_draw_panel_html(string $id, string $title, string $text, string $class = '', string $name = '')
{
    return "
        <div class='panel {$class}' id='{$id}'>
          <div class='panel-header'>
            <h3 class='panel-title'>{$title}</h3>
          </div>
          <div class='panel-body'>
            <p>{$text}</p>
          </div>
          <!-- /.panel-body -->
        </div>
        <!-- /.panel -->";
}

function tcs_draw_well_html(string $text, string $class = '', string $id = '', string $title = '', string $name = '')
{
    return "
        <div class='well {$class}'>
          $text
          <!-- /.well-body -->
        </div>
        <!-- /.well -->";
}
/**
 * Generates an input html element with a set of buttons attached to it.
 *
 * @param string $type The type of input element to generate.
 * @param string $name The name of the input element.
 * @param string $id The id of the input element.
 * @param string $placeholder The placeholder text for the input element.
 * @param string $label The label text for the input element.
 * @param string $value The value of the input element.
 * @param string $class The class of the input element.
 * @param string $required The required attribute of the input element.
 * @param array $buttons An array of buttons to attach to the input element.
 *
 * @return string The generated html.
 */
function tcs_draw_input_html(
    string $type,
    string $name,
    string|null $id = '',
    string|null $placeholder = '',
    string|null $label = '',
    string|null $value = '',
    string|null $class = '',
    string|null $required = '',
    array|null $buttons = []
) {
    $buttons_html = '';
    $button_described = '';

    if (sizeof($buttons) > 0) {
        foreach ($buttons as $button) {
            $buttons_html .= generate_button_html(
                $button['method'],
                $button['endpoint'],
                $button['target'],
                $button['swap'],
                $button['text'],
                $button['vals'] ?? null,
                $button['extra_params'] ?? ''
            );
            $button_described = 'aria-describedby="' . $button['id'] . '"';
        }
    }
    if ($required == 'true') $required = 'required';
    if (isset($label)) $label_html = '<label class="control-label" for="' . $id . '">' . $label . '</label>';
    else $label_html = '';
    if ($id == ''){ $id = $name; }

    $id = ($id != null) ? ' id="' . $id . '"' : '';
    $class = ($class != null) ? ' class="' . $class . '"' : ' class="form-control variations_form"';
    $placeholder = ($placeholder != null) ? ' placeholder="' . $placeholder . '"' : '';
    $aria_label = ($label != null) ? ' aria-label="' . $label . '"' : '';
    $required = ($required != null) ? ' ' . $required : '';
    return '<div class="input-group mb-3">' . $label_html . '<input type="' . $type . '" name="' . $name . '"   value = "' . $value . '" ' . $id . $class . $placeholder . $aria_label . $required . '>' . $buttons_html . '</div>';
}

/**
 * Generates HTML for a textarea form control
 *
 * @param string $name
 * @param string $id
 * @param string|null $placeholder
 * @param string|null $label
 * @param string|null $value
 * @param string|null $class
 * @param string|null $required
 * @return string
 */
function tcs_draw_textarea_html(
    string $name,
    string $id,
    string|null $placeholder = '',
    string|null $label = '',
    string|null $value = '',
    string|null $class = '',
    string|null $required = '',
) {
    return '<div class="input-group mb-3">' . '<textarea name="' . $name . '" id="' . $id . '" class="' . ($class ?? 'form-control variations_form') . '" placeholder="' . $placeholder . '" value = "' . $value . '" aria-label="' . $label . '"' . $required . ' rows="10"></textarea></div>';
}

/**
 * Generates an HTML form element with the given parameters.
 * @param string $method The HTTP method to use for the form submission.
 * @param string $endpoint The endpoint to submit the form to.
 * @param string|null $name The name of the form.
 * @param string|null $id The ID of the form.
 * @param string|null $class The class of the form.
 * @param string|null $swap The swap strategy to use for the form submission.
 * @param string|null $target The target element to swap.
 * @param array $vals The values to include in the form submission.
 * @return string The generated HTML form element.
 */
function tcs_draw_form_html(string $method, string $endpoint, string $name = null, string $id = null, string $class = null, string $swap = null, string $target = null, array $vals = [])
{
    $vals_attribute = $vals ? "hx-vals='" . json_encode($vals) . "'" : '';
    $id = ($id != null) ?  ' id="' . $id . '"' : '';
    $name = ($name != null) ?  ' name="' . $name . '"' : '';
    $class = ($class != null) ?  ' class="' . $class . '"' : '';
    $method = ($method != null) ?  $method = 'post' : '';
    $endpoint = ($endpoint != null) ?  ' hx-' . $method . '="' . $endpoint . '"' : '';
    $target = ($target != null) ?  ' hx-target="' . $target . '"' : '';
    $swap = ($swap != null) ?  ' hx-swap="' . $swap . '"' : '';
    return '<form ' . $id . $name . $endpoint . $swap . $vals_attribute . $target . '>';
}
/**
 * Generates an HTML label element for a form control.
 *
 * @param string $text The text of the label.
 * @param string $for The id of the form control that the label is for.
 * @return string The generated HTML label element.
 */
function tcs_draw_label_html(string $text, string $for)
{
    return '<label for="' . $for . '">' . $text . '</label>';
}

/**
 * Generates a <button type="submit"> element.
 *
 * @param string $class The class of the element.
 * @param string $text The text of the element.
 * @return string The generated html.
 */
function tcs_draw_submit_html(string $class, string $text)
{
    return '<button type="submit" class="' . $class . '">' . $text . '</button>';
}

/**
 * Generates a <select> element.
 *
 * @param string $name The name of the element.
 * @param string $id The id of the element.
 * @param array $options An associative array of options, where the keys are the values and the values are the texts.
 * @param string|null $selected The value of the selected option.
 * @param array|null $htmx An associative array of htmx attributes.
 * @return string The generated html.
 */
function tcs_draw_select_html(string $name, string $id = '', array $options = [], string|null $selected = null, array|null $htmx = null)
{
    $htmx_out = '';

    if ($htmx != null) {
        $method = ($htmx['hx-method'] != null) ?  $method = $htmx['hx-method'] : '';
        $htmx_out .= ($htmx['hx-endpoint'] != null) ?  ' hx-' . $method . '="' . $htmx['hx-endpoint'] . '"' : '';
        $htmx_out .= isset($htmx['hx-target']) ? ' hx-target="' . $htmx['hx-target'] . '"' : '';
        $htmx_out .= isset($htmx['hx-swap']) ? ' hx-swap="' . $htmx['hx-swap'] . '"' : '';
        $htmx_out .= isset($htmx['hx-vals']) ? ' hx-vals=\'' . json_encode($htmx['hx-vals']) . '\'' : '';
        $htmx_out .= isset($htmx['hx-indicator']) ? ' hx-indicator="' . $htmx['hx-indicator'] . '"' : ' hx-indicator="#indicatorLines"';
        $htmx_out .= isset($htmx['hx-trigger']) ? ' hx-trigger="' . $htmx['hx-trigger'] . '"' : '';
        $htmx_out .= isset($htmx['hx-include']) ? ' hx-include="' . $htmx['hx-include'] . '"' : '';
    }
    if ($id != '') {
        $id = 'id="' . $id . '"';
    }
    $output = '<select name="' . $name . '" $id="' . $id . '"  class="form-control variations_form" ' . $htmx_out . '>';
    if (count($options) > 0) {
        foreach ($options as $value => $option) {
            $value = !is_numeric($value) ? $value : $option;
            $text = $option;
            $selected_attr = ($text == $selected) ? 'selected' : '';
            $output .= '<option value="' . $value . '" ' . $selected_attr . '>' . $text . '</option>';
        }
    }
    $output .= '</select>';
    return $output;
}


/**
 * Output a table for administrative purposes with a title, header, body, and footer
 *
 * @param string $title Table title
 * @param string $id Table ID
 * @param string $class Table classes
 * @param array $columns Table column definitions. Each column is an associative array with keys 'name' and 'class'.
 * @param array $dataset Table row data. Each row is an associative array with keys 'id', 'class', and 'content'.
 * @param string $footer Table footer content
 * @return string The rendered table
 */
function tcs_draw_admin_bootstrap_table($title, $id = "", $class = "", $columns, $rows, $footer = ""){
    $table_container = "
    <div class='panel panel-default'>
        <div class='panel-header'>
        <div class='panel-body'>{$title}</div></div>";

    $table_header = "<table id='{$id}' class='{$class}'><thead><tr>";
    foreach ($columns as $column) {
        extract($column, EXTR_PREFIX_ALL, "col"); // Extract columns into $col_name, $col_class
        if (!empty($col_params) && is_array($col_params)) {
            foreach ($params as $key => $value) {
                $attributes .= " data-{$key}='{$value}'";
            }
        }
        $table_header .= "<td class='{$col_class}' id='{$col_id}' {$col_params}>{$col_name}</td>" . PHP_EOL;
    }
    $table_header .=  "</tr></thead>";
    $table_body = "<tbody  id='{$id}_body'>";
    $table_rows = $rows;
    $table_body .= $table_rows;
    $table_body .= "</tbody>";
    $table_footer = "</table><div class='panel-footer'>{$footer}</div></div>";
    ////print_rr($table_container . $table_header . $table_body . $table_footer);
    return $table_container . $table_header . $table_body . $table_footer;
}

/**
 * Outputs a table row for use in an admin table.
 *
 * @param string $row_id The id of the row
 * @param string $row_class The class of the row
 * @param array $row_content The content of the row. Each cell is an associative array with keys 'class', 'id', 'text', 'data', and 'content'.
 * @return string The rendered row
 */
function tcs_draw_admin_bootstrap_table_row($row_id = "", $row_class = "", $row_content = [], $params = []){
    ////print_rr($row_content, 'row_content');
    $params_string = '';
    foreach ($params as $key => $param){
        $params_string .= " {$key}='{$param}' ";
    }
    $row = "<tr id='{$row_id}' class='{$row_class}'{$params_string}>";
    foreach ($row_content as $cell) {
        ////print_rr($cell, 'rcell_content');
        $row .= "<td";
        foreach ($cell as $key => $value) {
            if ($key == 'content') continue;
            if ($key == 'params') {
                foreach ($value as $key => $value) {
                    $row .= " $key='$value'";
                }
                continue;
            }
            $row .= " $key='$value'";
        }
        $row .= ">{$cell['content']}</td>";
        ////print_rr($row);
    }
    $row .= '</tr>';
    return $row;
}

// specific components

function tcs_draw_product_options_select($products_attributes, $products_id)
{
    $products_options_name_query = tep_db_query("SELECT DISTINCT popt.products_options_id, popt.products_options_name FROM products_options popt, products_attributes patrib WHERE patrib.products_id='{$products_id}' AND patrib.options_id = popt.products_options_id ORDER BY popt.products_options_name");

    if (tep_db_num_rows($products_options_name_query)) {
        while ($products_options_name = tep_db_fetch_array($products_options_name_query)) {
            $select_output = '<div class="form-group attribute_group form-inline">
                            <label for="input_' . $products_options_name['products_options_id'] . '" class="control-label"> ' . $products_options_name['products_options_name'] . ': </label>
                            <select name="id[' . $products_options_name['products_options_id'] . ']" class="form-control variations_form variationsAttributeSelect">';

            $products_options_query = tep_db_query("SELECT pov.products_options_values_id, pov.products_options_values_name FROM products_options_values pov, products_attributes pa WHERE pa.products_id='{$products_id}' AND pa.options_id='{$products_options_name['products_options_id']}' AND pa.options_values_id=pov.products_options_values_id");

            $select_output_options = '<option disabled selected value>None</option>';
            while ($products_options = tep_db_fetch_array($products_options_query)) {
                $select_output_options .= '<option value="' . $products_options['products_options_values_id'] . '">' . $products_options['products_options_values_name'] . '</option>';
            }
            $select_output .= $select_output_options . '</select></div>';
            return $select_output;
        }
    }
}


function renderForm($config,$values = []) {
    $formContent = render_node($config['form'],$config['form']['<cfg>']['common'] ?? [],$values);
    if ($config['form']['<cfg>']['noTag']) return "<div class='\"'row {$config['form']['<cfg>']['class']}' id='{$config['form']['<cfg>']['id']}'>"  .  $formContent . "</DIV>";
    return "<form class='{$config['form']['<cfg>']['class']}' id='{$config['form']['<cfg>']['id']}'>
                {$formContent}
            </form>";
}

function render_node($nodes,$common = [],$values = []) {

    $formContent = '';
    if (is_array($nodes['<common>'])) $common = array_merge($common, $nodes['<common>']);
    foreach ($nodes as $node_name => $node) {
        if ($node_name == "<common>" || $node_name == "<cfg>") continue;
        switch ($node['node_type'] ?? "element") {
            case 'group':
                $formContent .= renderGroup($node,$common,$values);
                break;
            default:
                ////print_rr($node,"node_name: " . $node_name);
                ////print_rr($node,"node_common: " . $node_name);
                if (is_array($node)) $node['name'] = $node_name;
                $formContent .= renderElement($node,$common,$values);
        }
   }
   return $formContent;
}

function renderGroup($group, $common = [],$values = []) {
    $common = array_merge($common, $group['elements']['<common>'] ?? []);
    $groupHtml = "<div class='" . $group['class'] . " '>";
    $groupHtml .= render_node($group['elements'], $common,$values);
    $groupHtml .= "</div>";
    return $groupHtml;
}

function renderElement($elementConfig, $common = [],$values = []) {

    $elementConfig = array_merge($common, $elementConfig);
    foreach ($elementConfig as $key => $value) {
        $value = str_replace('<name>',  $elementConfig['name'], $value);
        $value = str_replace('<id>',    $elementConfig['id'], $value);
        $value = str_replace('<label>', $elementConfig['label'], $value);
        $value = str_replace('<node_type>',  $elementConfig['node_type'], $value);

        switch ($key){
            case 'label':
                $value = mb_convert_case(str_replace('_', ' ',$value), MB_CASE_TITLE, "UTF-8");
            break;
            case 'name':
                $value = strtolower($value);
            break;
            case 'value':
                print_rr($values,'values feg:' . $elementConfig['name']);
                if ($value == "<values_list>"){
                    $value = '';
                    $theName = $elementConfig['name'];
                    if (isset($values[$theName])) $value = $values[$theName];
                    //print_rr($values[$theName],'$values[$theName]: ' . '$values[' . $theName . ']' . $values[$theName]);
                };
                $elementConfig["values_name"] =  $values[$elementConfig['name'] . '_value_name'];
            break;
        }
        $elementConfig[$key] = $value;


    }
    //print_rr($values,"values");
    switch ($elementConfig['node_type']) {
        case 'input': return renderInput($elementConfig);
        case 'select': return renderSelect($elementConfig);
        case 'button': return renderButton($elementConfig);
        default: return '';
    }
}




function renderInput($config) {
    ////print_rr($config,'config flarp');
    $label = $config['label'] ?? '';

    $type = $config['type'] ?? 'text';

    unset($config['label']);
    unset($config['node_type']);
    $inputHtml = '';
    if ($type != 'hidden' && $label != '') $inputHtml = "<label for=\"{$config['id']}\">{$label}</label>";
    $input_attribs = '';
    foreach ($config as $key => $value) {
        $input_attribs .= " $key='$value' ";
    }
    $inputHtml .= "<input{$input_attribs}>";

    return $inputHtml;
}

function renderSelect($config){
    $name = $config['name'] ?? '';
    $class = $config['class'] ?? "";
    $id = $config['id'] ?? "";
    $label = $config['label'] ?? "";

    $selectHtml = $label ? "<label for='$id'>$label</label>" : '';
    $selectHtml .= "<select name='$name' id='$id' class='$class'>";
    print_rr($config,'select config');
    if (!is_null($config['value']) && $config['value'] != ''){
        if(!isset($config['options'])){
            $selectHtml .= "<option value='{$config['value']}' selected>{$config['values_name']}</option>";
        }
    }

    if (isset($config['options'])) {
        foreach ($config['options'] as $value => $option) {
            $selected = '';
            if (isset($config['value']) && $config['value'] == $value) $selected = 'selected';
            $selectHtml .= "<option value='{$value}'{$selected}>{$option}</option>";
        }
    }
    $selectHtml .= "</select>";
    return $selectHtml;
}


function renderButton($config) {
    $name = $config['name'] ?? '';
    $class = $config['class'] ?? '';
    $id = $config['id'] ?? '';
    $label = $config['label'] ?? 'Submit';
    $with_transitions = $config['with_transitions'] ?? true;

    // Add Alpine.js transition classes if enabled
    if ($with_transitions) {
        $class .= ' transition-all duration-200 hover:scale-105';
    }

    $attributes = [
        'type' => 'button',
        'id' => $id,
        'class' => $class,
        'hx-trigger' => $config['hx-trigger'] ?? null,
        'hx-get' => $config['hx-get'] ?? null,
        'hx-post' => $config['hx-post'] ?? null,
        'hx-target' => $config['hx-target'] ?? null,
        'hx-include' => $config['hx-include'] ?? null,
        'hx-params' => $config['hx-params'],
        'hx-swap' => $config['hx-swap'] ?? null
    ];

    // Add Alpine.js attributes for loading states
    if ($with_transitions) {
        $attributes['x-data'] = '{ loading: false }';
        $attributes['x-on:htmx:before-request'] = 'loading = true';
        $attributes['x-on:htmx:after-request'] = 'loading = false';
        $attributes['x-bind:disabled'] = 'loading';
    }

    $buttonHtml = "<button ";
    foreach ($attributes as $attr => $value) {
        if ($value !== null) {
            $buttonHtml .= "{$attr}='{$value}' ";
        }
    }

    // Add loading text with Alpine.js
    $button_content = $with_transitions ?
        "<span x-show=\"!loading\">{$label}</span><span x-show=\"loading\" x-cloak>Loading...</span>" :
        $label;

    $buttonHtml .= ">{$button_content}</button>";

    return $buttonHtml;
}

/**
 * Creates an Alpine.js transition wrapper for HTMX content updates
 *
 * @param string $id The ID of the wrapper element
 * @param string $content The content to wrap
 * @param string $transition_type The type of transition (fade, slide, scale)
 * @param array $extra_attrs Additional attributes for the wrapper
 * @return string The HTML wrapper with Alpine.js transitions
 */
function tcs_create_transition_wrapper(string $id, string $content, string $transition_type = 'fade', array $extra_attrs = [])
{
    // Use simpler Alpine.js transitions that work better with HTMX
    $transition_attr = 'x-transition';

    $extra_attrs_str = '';
    foreach ($extra_attrs as $key => $value) {
        $extra_attrs_str .= ' ' . $key . '="' . $value . '"';
    }

    return '<div id="' . $id . '" x-data="{ show: true }" x-show="show" ' . $transition_attr . $extra_attrs_str . '>' . $content . '</div>';
}

/**
 * Creates an Alpine.js enhanced table row with transitions
 *
 * @param string $id The ID of the table row
 * @param string $class The CSS class for the row
 * @param array $content Array of cell content
 * @param array $params Additional parameters
 * @param bool $with_transitions Whether to include Alpine.js transitions
 * @return string The HTML table row with Alpine.js enhancements
 */
function tcs_draw_admin_bootstrap_table_row_with_transitions(string $id, string $class, array $content, array $params = [], bool $with_transitions = true)
{
    $row_html = tcs_draw_admin_bootstrap_table_row($id, $class, $content, $params);

    if ($with_transitions) {
        // Wrap the row with Alpine.js transition
        $row_html = '<tbody x-data="{ show: true }" x-show="show" x-transition:enter="table-row-enter-active" x-transition:enter-start="table-row-enter" x-transition:enter-end="table-row-enter-to" x-transition:leave="table-row-leave-active" x-transition:leave-start="table-row-leave" x-transition:leave-end="table-row-leave-to">' . $row_html . '</tbody>';
    }

    return $row_html;
}

