<?php
/**
 * Example API endpoint for Alpine.js + HTMX transitions demo
 * This file provides sample responses for testing the transition functionality
 */

// Enable CORS for development
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE');
header('Access-Control-Allow-Headers: Content-Type');

// Get the action parameter
$action = $_GET['action'] ?? $_POST['action'] ?? '';

// Add a small delay to demonstrate loading states
if (!isset($_GET['no_delay'])) {
    usleep(500000); // 0.5 second delay
}

switch ($action) {
    case 'example_action':
        handleExampleAction();
        break;
        
    case 'get_content':
        handleGetContent();
        break;
        
    case 'submit_form':
        handleSubmitForm();
        break;
        
    case 'add_row':
        handleAddRow();
        break;
        
    case 'slow_request':
        handleSlowRequest();
        break;
        
    case 'delete_row':
        handleDeleteRow();
        break;
        
    case 'update_content':
        handleUpdateContent();
        break;
        
    case 'load_more':
        handleLoadMore();
        break;
        
    case 'search':
        handleSearch();
        break;
        
    default:
        http_response_code(400);
        echo json_encode(['error' => 'Unknown action: ' . $action]);
        break;
}

function handleExampleAction() {
    $test_value = $_POST['test'] ?? 'No value';
    
    echo '<div class="alert alert-success" x-data="{ show: true }" x-show="show" x-transition>
        <h5>Success!</h5>
        <p>The HTMX request completed successfully.</p>
        <p><strong>Test value received:</strong> ' . htmlspecialchars($test_value) . '</p>
        <p><strong>Timestamp:</strong> ' . date('Y-m-d H:i:s') . '</p>
        <button class="btn btn-sm btn-outline-success" x-on:click="show = false">Dismiss</button>
    </div>';
}

function handleGetContent() {
    $content_options = [
        '<div class="alert alert-info">
            <h5>Dynamic Content #1</h5>
            <p>This content was loaded dynamically with a fade transition!</p>
            <p>Random number: ' . rand(1, 1000) . '</p>
        </div>',
        '<div class="alert alert-warning">
            <h5>Dynamic Content #2</h5>
            <p>Here\'s some different content to show the transition effect.</p>
            <p>Current time: ' . date('H:i:s') . '</p>
        </div>',
        '<div class="alert alert-primary">
            <h5>Dynamic Content #3</h5>
            <p>Each click loads new content with smooth transitions.</p>
            <p>Random color: #' . substr(md5(rand()), 0, 6) . '</p>
        </div>'
    ];
    
    echo $content_options[array_rand($content_options)];
}

function handleSubmitForm() {
    $name = $_POST['name'] ?? '';
    $email = $_POST['email'] ?? '';
    
    // Simulate validation
    if (empty($name) || empty($email)) {
        http_response_code(400);
        echo '<div class="alert alert-danger" x-data="{ show: true }" x-show="show" x-transition>
            <h5>Validation Error</h5>
            <p>Please fill in all required fields.</p>
        </div>';
        return;
    }
    
    // Simulate success
    echo '<div class="alert alert-success" x-data="{ show: true }" x-show="show" x-transition>
        <h5>Form Submitted Successfully!</h5>
        <p><strong>Name:</strong> ' . htmlspecialchars($name) . '</p>
        <p><strong>Email:</strong> ' . htmlspecialchars($email) . '</p>
        <p><strong>Submitted at:</strong> ' . date('Y-m-d H:i:s') . '</p>
    </div>';
}

function handleAddRow() {
    static $row_counter = 2; // Start from 2 since we have one example row
    $row_counter++;
    
    $statuses = ['Active', 'Inactive', 'Pending', 'Completed'];
    $names = ['New Item', 'Sample Data', 'Test Entry', 'Demo Record', 'Example Row'];
    
    $status = $statuses[array_rand($statuses)];
    $name = $names[array_rand($names)] . ' ' . $row_counter;
    
    echo '<tr x-data="tableRow()" x-show="show" x-transition:enter="table-row-enter-active" x-transition:enter-start="table-row-enter" x-transition:enter-end="table-row-enter-to" x-transition:leave="table-row-leave-active" x-transition:leave-start="table-row-leave" x-transition:leave-end="table-row-leave-to">
        <td>' . $row_counter . '</td>
        <td>' . htmlspecialchars($name) . '</td>
        <td><span class="badge bg-' . ($status === 'Active' ? 'success' : ($status === 'Inactive' ? 'danger' : 'warning')) . '">' . $status . '</span></td>
        <td>
            <button 
                class="btn btn-sm btn-danger transition-all duration-200 hover:scale-105"
                x-on:click="remove()"
                hx-post="api_e.php?action=delete_row"
                hx-vals=\'{"id": "' . $row_counter . '"}\'
                hx-target="closest tr"
                hx-swap="delete"
            >Delete</button>
            <button 
                class="btn btn-sm btn-primary transition-all duration-200 hover:scale-105"
                hx-get="api_e.php?action=update_content"
                hx-target="closest tr"
                hx-swap="outerHTML"
            >Edit</button>
        </td>
    </tr>';
}

function handleSlowRequest() {
    // Add extra delay for this request
    sleep(2);
    
    echo '<div class="alert alert-info" x-data="{ show: true }" x-show="show" x-transition>
        <h5>Slow Request Completed!</h5>
        <p>This request took 2 seconds to complete, demonstrating the loading indicator.</p>
        <p>The global loading spinner should have been visible during the request.</p>
        <p><strong>Completed at:</strong> ' . date('Y-m-d H:i:s') . '</p>
    </div>';
}

function handleDeleteRow() {
    $id = $_POST['id'] ?? '';
    
    // Simulate deletion
    echo '<div class="alert alert-warning" x-data="{ show: true }" x-show="show" x-transition>
        <p>Row ' . htmlspecialchars($id) . ' has been deleted.</p>
    </div>';
}

function handleUpdateContent() {
    echo '<tr x-data="tableRow()" x-show="show" x-transition>
        <td colspan="4">
            <div class="p-3 bg-light rounded">
                <h6>Edit Mode</h6>
                <div class="row">
                    <div class="col-md-4">
                        <input type="text" class="form-control form-control-sm" placeholder="Name" value="Updated Item">
                    </div>
                    <div class="col-md-3">
                        <select class="form-control form-control-sm">
                            <option>Active</option>
                            <option>Inactive</option>
                            <option>Pending</option>
                        </select>
                    </div>
                    <div class="col-md-5">
                        <button class="btn btn-sm btn-success">Save</button>
                        <button class="btn btn-sm btn-secondary" 
                                hx-get="api_e.php?action=add_row" 
                                hx-target="closest tr" 
                                hx-swap="outerHTML">Cancel</button>
                    </div>
                </div>
            </div>
        </td>
    </tr>';
}

function handleLoadMore() {
    $items = [];
    for ($i = 1; $i <= 3; $i++) {
        $items[] = '<div class="card mb-2" x-data="{ show: true }" x-show="show" x-transition>
            <div class="card-body">
                <h6 class="card-title">Loaded Item #' . rand(100, 999) . '</h6>
                <p class="card-text">This item was loaded dynamically with transitions.</p>
                <small class="text-muted">Loaded at: ' . date('H:i:s') . '</small>
            </div>
        </div>';
    }
    
    echo implode('', $items);
}

function handleSearch() {
    $query = $_GET['q'] ?? $_POST['q'] ?? '';
    
    if (empty($query)) {
        echo '<div class="alert alert-info">Enter a search term to see results.</div>';
        return;
    }
    
    $results = [
        'apple' => ['Apple iPhone', 'Apple MacBook', 'Apple Watch'],
        'samsung' => ['Samsung Galaxy', 'Samsung TV', 'Samsung Tablet'],
        'google' => ['Google Pixel', 'Google Nest', 'Google Chrome'],
        'microsoft' => ['Microsoft Surface', 'Microsoft Xbox', 'Microsoft Office']
    ];
    
    $found_results = [];
    foreach ($results as $key => $items) {
        if (stripos($key, $query) !== false) {
            $found_results = array_merge($found_results, $items);
        }
    }
    
    if (empty($found_results)) {
        echo '<div class="alert alert-warning" x-data="{ show: true }" x-show="show" x-transition>
            No results found for "' . htmlspecialchars($query) . '"
        </div>';
        return;
    }
    
    echo '<div x-data="{ show: true }" x-show="show" x-transition>';
    echo '<h6>Search Results for "' . htmlspecialchars($query) . '":</h6>';
    echo '<ul class="list-group">';
    foreach ($found_results as $result) {
        echo '<li class="list-group-item">' . htmlspecialchars($result) . '</li>';
    }
    echo '</ul>';
    echo '</div>';
}

// Helper function to generate random data
function generateRandomData($type = 'item') {
    $adjectives = ['Amazing', 'Fantastic', 'Incredible', 'Outstanding', 'Remarkable'];
    $nouns = ['Product', 'Service', 'Solution', 'Tool', 'Platform'];
    
    return $adjectives[array_rand($adjectives)] . ' ' . $nouns[array_rand($nouns)];
}

// Error handling
function handleError($message) {
    http_response_code(500);
    echo '<div class="alert alert-danger" x-data="{ show: true }" x-show="show" x-transition>
        <h5>Error</h5>
        <p>' . htmlspecialchars($message) . '</p>
    </div>';
}
?>
