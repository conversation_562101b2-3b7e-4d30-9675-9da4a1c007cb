<!DOCTYPE html>
<html>
<head>
    <title>Alpine.js Debug Test</title>
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        [x-cloak] { display: none !important; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1>Alpine.js Debug Test</h1>
        
        <!-- Basic Alpine.js test -->
        <div class="card mb-4">
            <div class="card-header">
                <h3>Basic Alpine.js Test</h3>
            </div>
            <div class="card-body">
                <div x-data="{ message: 'Hello Alpine.js!', count: 0 }">
                    <p x-text="message"></p>
                    <button @click="count++" class="btn btn-primary">
                        Clicked <span x-text="count"></span> times
                    </button>
                </div>
            </div>
        </div>

        <!-- Transition test -->
        <div class="card mb-4">
            <div class="card-header">
                <h3>Transition Test</h3>
            </div>
            <div class="card-body">
                <div x-data="{ show: true }">
                    <button @click="show = !show" class="btn btn-success">
                        Toggle Content
                    </button>
                    <div x-show="show" x-transition class="mt-3 alert alert-info">
                        <h5>Transition Test</h5>
                        <p>This should fade in and out smoothly.</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Loading state test -->
        <div class="card mb-4">
            <div class="card-header">
                <h3>Loading State Test</h3>
            </div>
            <div class="card-body">
                <div x-data="{ loading: false }">
                    <button 
                        @click="loading = true; setTimeout(() => loading = false, 2000)" 
                        :disabled="loading"
                        class="btn btn-warning"
                    >
                        <span x-show="!loading">Start Loading</span>
                        <span x-show="loading" x-cloak>Loading...</span>
                    </button>
                    <div x-show="loading" x-transition class="mt-3 alert alert-warning">
                        <div class="spinner-border spinner-border-sm me-2"></div>
                        Loading for 2 seconds...
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Debug Alpine.js
        document.addEventListener('alpine:init', () => {
            console.log('✅ Alpine.js initialized successfully!');
        });

        // Check if Alpine is available
        setTimeout(() => {
            if (window.Alpine) {
                console.log('✅ Alpine.js is available on window object');
                console.log('Alpine version:', Alpine.version || 'Unknown');
            } else {
                console.error('❌ Alpine.js is not available');
            }
        }, 1000);
    </script>
</body>
</html>
