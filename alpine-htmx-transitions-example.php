<?php
/**
 * Example file demonstrating Alpine.js transitions with HTMX
 * This file shows how to use the enhanced components with transitions
 */

// Include the enhanced components
require_once('includes/functions/tcs_components.php');
require_once('includes/functions/tcs_attributes_components.php');

?>
<!DOCTYPE html>
<html>
<head>
    <title>Alpine.js + HTMX Transitions Example</title>
    <script src="https://unpkg.com/htmx.org@2.0.2"></script>
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="../stylesheet.css">
    <script src="includes/javascript/alpine-htmx-transitions.js"></script>
</head>
<body>
    <div class="container mt-4">
        <h1>Alpine.js + HTMX Transitions Demo</h1>
        
        <!-- Example 1: Enhanced <PERSON><PERSON> with Loading State -->
        <div class="card mb-4">
            <div class="card-header">
                <h3>Enhanced Button with Loading State</h3>
            </div>
            <div class="card-body">
                <p>This button uses Alpine.js to show loading states during HTMX requests:</p>
                <?php
                echo tcs_draw_button_html(
                    'post',
                    'api_h.php?action=example_action',
                    '#result1',
                    'innerHTML',
                    'Click Me!',
                    ['test' => 'value'],
                    [],
                    'btn-success',
                    true // Enable transitions
                );
                ?>
                <div id="result1" class="mt-3 p-3 border rounded" style="min-height: 50px;">
                    Results will appear here...
                </div>
            </div>
        </div>

        <!-- Example 2: Transition Wrapper -->
        <div class="card mb-4">
            <div class="card-header">
                <h3>Content with Fade Transition</h3>
            </div>
            <div class="card-body">
                <button 
                    class="btn btn-primary"
                    hx-get="api_h.php?action=get_content"
                    hx-target="#fade-content"
                    hx-swap="innerHTML"
                >Load Content with Fade</button>
                
                <?php
                echo tcs_create_transition_wrapper(
                    'fade-content',
                    '<p class="text-muted">Click the button to load new content with a fade transition.</p>',
                    'fade',
                    ['class' => 'mt-3 p-3 border rounded']
                );
                ?>
            </div>
        </div>

        <!-- Example 3: Form with Alpine.js State Management -->
        <div class="card mb-4">
            <div class="card-header">
                <h3>Form with State Management</h3>
            </div>
            <div class="card-body">
                <div x-data="formState()">
                    <form 
                        hx-post="api_h.php?action=submit_form"
                        hx-target="#form-result"
                        x-on:htmx:before-request="submit()"
                        x-on:htmx:after-request="handleSuccess()"
                    >
                        <div class="mb-3">
                            <label class="form-label">Name:</label>
                            <input type="text" name="name" class="form-control" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Email:</label>
                            <input type="email" name="email" class="form-control" required>
                        </div>
                        <button 
                            type="submit" 
                            class="btn btn-primary transition-all duration-200 hover:scale-105"
                            x-bind:disabled="submitting"
                        >
                            <span x-show="!submitting">Submit</span>
                            <span x-show="submitting" x-cloak>Submitting...</span>
                        </button>
                    </form>
                    
                    <div 
                        x-show="success" 
                        x-transition
                        class="alert alert-success mt-3"
                        x-cloak
                    >
                        Form submitted successfully!
                    </div>
                    
                    <div id="form-result" class="mt-3"></div>
                </div>
            </div>
        </div>

        <!-- Example 4: Table Row with Transitions -->
        <div class="card mb-4">
            <div class="card-header">
                <h3>Table with Row Transitions</h3>
            </div>
            <div class="card-body">
                <button 
                    class="btn btn-success mb-3"
                    hx-post="api_h.php?action=add_row"
                    hx-target="#example-table tbody"
                    hx-swap="beforeend"
                >Add Row</button>
                
                <table id="example-table" class="table table-striped">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Name</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr x-data="tableRow()" x-show="show" x-transition:leave="table-row-leave-active">
                            <td>1</td>
                            <td>Sample Item</td>
                            <td>Active</td>
                            <td>
                                <button 
                                    class="btn btn-sm btn-danger"
                                    x-on:click="remove()"
                                >Delete</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Example 5: Loading Indicator -->
        <div class="card mb-4">
            <div class="card-header">
                <h3>Global Loading Indicator</h3>
            </div>
            <div class="card-body">
                <button 
                    class="btn btn-info"
                    hx-get="api_h.php?action=slow_request"
                    hx-target="#slow-result"
                    hx-indicator="#indicatorLines"
                >Trigger Slow Request</button>
                
                <div id="slow-result" class="mt-3 p-3 border rounded">
                    Results will appear here after a delay...
                </div>
            </div>
        </div>
    </div>

    <!-- Global Loading Indicator -->
    <div id="indicatorLines" style="display: none; position: fixed; top: 10px; right: 10px; z-index: 9999;">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
    </div>

    <script>
        // Example of manual transition control
        function demonstrateManualTransitions() {
            const element = document.getElementById('manual-demo');
            
            // Fade out
            AlpineHTMXUtils.fadeOut(element, 300);
            
            // After 1 second, fade back in
            setTimeout(() => {
                AlpineHTMXUtils.fadeIn(element, 300);
            }, 1000);
        }
    </script>
</body>
</html>
